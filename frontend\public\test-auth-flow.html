<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Authentication Flow Test</h1>
    
    <div class="section">
        <h2>1. Login Test</h2>
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>2. Token Verification Test</h2>
        <button onclick="testVerify()">Test Token Verification</button>
        <div id="verifyResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>3. LocalStorage Test</h2>
        <button onclick="checkLocalStorage()">Check LocalStorage</button>
        <button onclick="clearLocalStorage()">Clear LocalStorage</button>
        <div id="storageResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>4. Simulate Page Reload</h2>
        <button onclick="simulateReload()">Simulate Auth Restoration</button>
        <div id="reloadResult" class="result"></div>
    </div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    localStorage.setItem('auth_token', data.data.token);
                    resultDiv.innerHTML = `<div class="success">Login successful!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">Login failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testVerify() {
            const resultDiv = document.getElementById('verifyResult');
            const token = localStorage.getItem('auth_token');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">No token found. Please login first.</div>';
                return;
            }
            
            try {
                const response = await fetch('/api/auth/verify', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">Token verification successful!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">Token verification failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        function checkLocalStorage() {
            const resultDiv = document.getElementById('storageResult');
            const token = localStorage.getItem('auth_token');
            
            if (token) {
                resultDiv.innerHTML = `<div class="success">Token found in localStorage:</div>
                    <pre>${token}</pre>`;
            } else {
                resultDiv.innerHTML = '<div class="error">No token found in localStorage</div>';
            }
        }
        
        function clearLocalStorage() {
            localStorage.removeItem('auth_token');
            document.getElementById('storageResult').innerHTML = '<div class="success">LocalStorage cleared</div>';
        }
        
        async function simulateReload() {
            const resultDiv = document.getElementById('reloadResult');
            const token = localStorage.getItem('auth_token');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">No token found. Please login first.</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div>Simulating page reload authentication restoration...</div>';
            
            try {
                // This simulates what the AuthContext does on page load
                const response = await fetch('/api/auth/verify', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ Authentication would be restored successfully!</div>
                        <div>User would remain logged in as: ${data.data.name} (${data.data.email})</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Authentication restoration would fail: ${data.message}</div>
                        <div>User would be redirected to login page</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Authentication restoration would fail: ${error.message}</div>
                    <div>User would be redirected to login page</div>`;
            }
        }
        
        // Auto-check localStorage on page load
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
